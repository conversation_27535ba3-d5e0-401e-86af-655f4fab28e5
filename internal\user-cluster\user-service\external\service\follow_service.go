package service

import (
	"context"
	"fmt"
	"pxpat-backend/internal/user-cluster/user-service/dto"
	"pxpat-backend/internal/user-cluster/user-service/model"
	"pxpat-backend/internal/user-cluster/user-service/repository"
	"pxpat-backend/pkg/errors"

	"github.com/rs/zerolog/log"
)

// FollowService 关注服务
type FollowService struct {
	followRepo repository.FollowRepository
	userRepo   repository.UserRepository
}

// NewFollowService 创建关注服务实例
func NewFollowService(followRepo repository.FollowRepository, userRepo repository.UserRepository) *FollowService {
	return &FollowService{
		followRepo: followRepo,
		userRepo:   userRepo,
	}
}

// FollowUser 关注用户
func (s *FollowService) FollowUser(ctx context.Context, followerKSUID, followeeKSUID string) (*dto.FollowUserResponse, error) {
	// 检查是否尝试关注自己
	if followerKSUID == followeeKSUID {
		return &dto.FollowUserResponse{
			Success:       false,
			Message:       "不能关注自己",
			FolloweeKSUID: followeeKSUID,
			IsFollowing:   false,
		}, nil
	}

	// 检查被关注的用户是否存在
	followee, err := s.userRepo.GetByUserKSUID(ctx, followeeKSUID)
	if err != nil {
		if err == repository.ErrUserNotFound {
			return &dto.FollowUserResponse{
				Success:       false,
				Message:       "用户不存在",
				FolloweeKSUID: followeeKSUID,
				IsFollowing:   false,
			}, nil
		}
		log.Error().Err(err).Str("followee_ksuid", followeeKSUID).Msg("获取被关注用户信息失败")
		return nil, errors.NewInternalError("获取用户信息失败")
	}

	// 检查是否已经关注
	isFollowing, err := s.followRepo.IsFollowing(ctx, followerKSUID, followeeKSUID)
	if err != nil {
		log.Error().Err(err).Str("follower_ksuid", followerKSUID).Str("followee_ksuid", followeeKSUID).Msg("检查关注状态失败")
		return nil, errors.NewInternalError("检查关注状态失败")
	}

	if isFollowing {
		return &dto.FollowUserResponse{
			Success:       false,
			Message:       "已经关注了该用户",
			FolloweeKSUID: followeeKSUID,
			IsFollowing:   true,
		}, nil
	}

	// 创建关注关系
	follow := model.NewUserFollow(followerKSUID, followeeKSUID)
	err = s.followRepo.CreateFollow(ctx, follow)
	if err != nil {
		log.Error().Err(err).Str("follower_ksuid", followerKSUID).Str("followee_ksuid", followeeKSUID).Msg("创建关注关系失败")
		return nil, errors.NewInternalError("关注失败")
	}

	// 更新关注者的关注数量
	err = s.followRepo.UpdateUserFollowCounts(ctx, followerKSUID)
	if err != nil {
		log.Error().Err(err).Str("follower_ksuid", followerKSUID).Msg("更新关注者关注数量失败")
	}

	// 更新被关注者的粉丝数量
	err = s.followRepo.UpdateUserFollowCounts(ctx, followeeKSUID)
	if err != nil {
		log.Error().Err(err).Str("followee_ksuid", followeeKSUID).Msg("更新被关注者粉丝数量失败")
	}

	return &dto.FollowUserResponse{
		Success:       true,
		Message:       fmt.Sprintf("成功关注 %s", followee.Nickname),
		FolloweeKSUID: followeeKSUID,
		IsFollowing:   true,
	}, nil
}

// UnfollowUser 取消关注用户
func (s *FollowService) UnfollowUser(ctx context.Context, followerKSUID, followeeKSUID string) (*dto.FollowUserResponse, error) {
	// 检查是否尝试取消关注自己
	if followerKSUID == followeeKSUID {
		return &dto.FollowUserResponse{
			Success:       false,
			Message:       "不能取消关注自己",
			FolloweeKSUID: followeeKSUID,
			IsFollowing:   false,
		}, nil
	}

	// 检查是否已经关注
	isFollowing, err := s.followRepo.IsFollowing(ctx, followerKSUID, followeeKSUID)
	if err != nil {
		log.Error().Err(err).Str("follower_ksuid", followerKSUID).Str("followee_ksuid", followeeKSUID).Msg("检查关注状态失败")
		return nil, errors.NewInternalError("检查关注状态失败")
	}

	if !isFollowing {
		return &dto.FollowUserResponse{
			Success:       false,
			Message:       "未关注该用户",
			FolloweeKSUID: followeeKSUID,
			IsFollowing:   false,
		}, nil
	}

	// 删除关注关系
	err = s.followRepo.DeleteFollow(ctx, followerKSUID, followeeKSUID)
	if err != nil {
		log.Error().Err(err).Str("follower_ksuid", followerKSUID).Str("followee_ksuid", followeeKSUID).Msg("删除关注关系失败")
		return nil, errors.NewInternalError("取消关注失败")
	}

	// 更新关注者的关注数量
	err = s.followRepo.UpdateUserFollowCounts(ctx, followerKSUID)
	if err != nil {
		log.Error().Err(err).Str("follower_ksuid", followerKSUID).Msg("更新关注者关注数量失败")
	}

	// 更新被关注者的粉丝数量
	err = s.followRepo.UpdateUserFollowCounts(ctx, followeeKSUID)
	if err != nil {
		log.Error().Err(err).Str("followee_ksuid", followeeKSUID).Msg("更新被关注者粉丝数量失败")
	}

	return &dto.FollowUserResponse{
		Success:       true,
		Message:       "取消关注成功",
		FolloweeKSUID: followeeKSUID,
		IsFollowing:   false,
	}, nil
}

// GetFollowers 获取粉丝列表
func (s *FollowService) GetFollowers(ctx context.Context, userKSUID string, page, pageSize int) (*dto.FollowListResponse, error) {
	// 设置默认值
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	followers, total, err := s.followRepo.GetFollowers(ctx, userKSUID, page, pageSize)
	if err != nil {
		log.Error().Err(err).Str("user_ksuid", userKSUID).Msg("获取粉丝列表失败")
		return nil, errors.NewInternalError("获取粉丝列表失败")
	}

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	return &dto.FollowListResponse{
		Users:      followers,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}, nil
}

// GetFollowing 获取关注列表
func (s *FollowService) GetFollowing(ctx context.Context, userKSUID string, page, pageSize int) (*dto.FollowListResponse, error) {
	// 设置默认值
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	following, total, err := s.followRepo.GetFollowing(ctx, userKSUID, page, pageSize)
	if err != nil {
		log.Error().Err(err).Str("user_ksuid", userKSUID).Msg("获取关注列表失败")
		return nil, errors.NewInternalError("获取关注列表失败")
	}

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	return &dto.FollowListResponse{
		Users:      following,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}, nil
}

// CheckFollowStatus 检查关注状态
func (s *FollowService) CheckFollowStatus(ctx context.Context, currentUserKSUID, targetUserKSUID string) (*dto.CheckFollowStatusResponse, error) {
	// 检查是否关注对方
	isFollowing, err := s.followRepo.IsFollowing(ctx, currentUserKSUID, targetUserKSUID)
	if err != nil {
		log.Error().Err(err).Str("current_user", currentUserKSUID).Str("target_user", targetUserKSUID).Msg("检查关注状态失败")
		return nil, errors.NewInternalError("检查关注状态失败")
	}

	// 检查是否被对方关注
	isFollowedBy, err := s.followRepo.IsFollowing(ctx, targetUserKSUID, currentUserKSUID)
	if err != nil {
		log.Error().Err(err).Str("current_user", currentUserKSUID).Str("target_user", targetUserKSUID).Msg("检查被关注状态失败")
		return nil, errors.NewInternalError("检查关注状态失败")
	}

	return &dto.CheckFollowStatusResponse{
		IsFollowing:    isFollowing,
		IsFollowedBy:   isFollowedBy,
		IsMutualFollow: isFollowing && isFollowedBy,
	}, nil
}

// GetFollowStats 获取关注统计信息
func (s *FollowService) GetFollowStats(ctx context.Context, userKSUID string) (*dto.FollowStatsResponse, error) {
	stats, err := s.followRepo.GetFollowStats(ctx, userKSUID)
	if err != nil {
		log.Error().Err(err).Str("user_ksuid", userKSUID).Msg("获取关注统计失败")
		return nil, errors.NewInternalError("获取关注统计失败")
	}

	return stats, nil
}

// BatchCheckFollowStatus 批量检查关注状态
func (s *FollowService) BatchCheckFollowStatus(ctx context.Context, currentUserKSUID string, targetUserKSUIDs []string) (*dto.BatchCheckFollowResponse, error) {
	if len(targetUserKSUIDs) == 0 {
		return &dto.BatchCheckFollowResponse{
			FollowStatuses: []dto.UserFollowStatus{},
		}, nil
	}

	if len(targetUserKSUIDs) > 100 {
		return nil, errors.NewBadRequestError("最多只能检查100个用户的关注状态")
	}

	statuses, err := s.followRepo.BatchCheckFollowStatus(ctx, currentUserKSUID, targetUserKSUIDs)
	if err != nil {
		log.Error().Err(err).Str("current_user", currentUserKSUID).Msg("批量检查关注状态失败")
		return nil, errors.NewInternalError("批量检查关注状态失败")
	}

	return &dto.BatchCheckFollowResponse{
		FollowStatuses: statuses,
	}, nil
}

// CheckFollowersPrivacy 检查用户粉丝列表是否公开
func (s *FollowService) CheckFollowersPrivacy(ctx context.Context, userKSUID string) (bool, error) {
	user, err := s.userRepo.GetByUserKSUID(ctx, userKSUID)
	if err != nil {
		if err == repository.ErrUserNotFound {
			return false, errors.NewNotFoundError("用户不存在")
		}
		log.Error().Err(err).Str("user_ksuid", userKSUID).Msg("获取用户信息失败")
		return false, errors.NewInternalError("获取用户信息失败")
	}

	return user.IsFansPublic, nil
}

// CheckFollowingPrivacy 检查用户关注列表是否公开
func (s *FollowService) CheckFollowingPrivacy(ctx context.Context, userKSUID string) (bool, error) {
	user, err := s.userRepo.GetByUserKSUID(ctx, userKSUID)
	if err != nil {
		if err == repository.ErrUserNotFound {
			return false, errors.NewNotFoundError("用户不存在")
		}
		log.Error().Err(err).Str("user_ksuid", userKSUID).Msg("获取用户信息失败")
		return false, errors.NewInternalError("获取用户信息失败")
	}

	return user.IsFollowPublic, nil
}
