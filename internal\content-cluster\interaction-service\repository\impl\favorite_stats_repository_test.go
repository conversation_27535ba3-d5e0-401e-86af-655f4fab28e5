package impl

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"pxpat-backend/internal/content-cluster/interaction-service/model"
	"pxpat-backend/pkg/ksuid"
)

// TestFavoriteStatsModel 测试FavoriteStats模型的基本功能
func TestFavoriteStatsModel(t *testing.T) {
	contentKSUID := ksuid.GenerateKSUID()
	contentType := model.ContentTypeVideo

	// 测试创建新的收藏统计记录
	stats := model.NewFavoriteStats(contentKSUID, contentType)
	assert.NotNil(t, stats)
	assert.NotEmpty(t, stats.StatsKSUID)
	assert.Equal(t, contentKSUID, stats.ContentKSUID)
	assert.Equal(t, contentType, stats.ContentType)
	assert.Equal(t, int64(0), stats.FavoriteCount)

	// 测试增加收藏数量
	stats.IncrementFavorite()
	assert.Equal(t, int64(1), stats.FavoriteCount)

	stats.IncrementFavorite()
	assert.Equal(t, int64(2), stats.FavoriteCount)

	// 测试减少收藏数量
	stats.DecrementFavorite()
	assert.Equal(t, int64(1), stats.FavoriteCount)

	// 测试减少到0
	stats.DecrementFavorite()
	assert.Equal(t, int64(0), stats.FavoriteCount)

	// 测试不能减少到负数
	stats.DecrementFavorite()
	assert.Equal(t, int64(0), stats.FavoriteCount)

	// 测试更新数量
	stats.UpdateCount(100)
	assert.Equal(t, int64(100), stats.FavoriteCount)

	// 测试判断是否受欢迎
	assert.True(t, stats.IsPopular())

	stats.UpdateCount(50)
	assert.False(t, stats.IsPopular())

	// 测试判断是否热门
	stats.UpdateCount(1000)
	assert.True(t, stats.IsHot())

	stats.UpdateCount(500)
	assert.False(t, stats.IsHot())
}

// TestContentTypeValidation 测试内容类型验证
func TestContentTypeValidation(t *testing.T) {
	// 测试有效的内容类型
	validTypes := []model.ContentType{
		model.ContentTypeVideo,
		model.ContentTypeShort,
		model.ContentTypeAnime,
		model.ContentTypeNovel,
	}

	for _, contentType := range validTypes {
		assert.True(t, model.IsValidContentType(contentType), "Content type %s should be valid", contentType)
	}

	// 测试无效的内容类型
	invalidType := model.ContentType("invalid")
	assert.False(t, model.IsValidContentType(invalidType), "Invalid content type should not be valid")
}

// TestTableName 测试表名
func TestTableName(t *testing.T) {
	stats := &model.FavoriteStats{}
	assert.Equal(t, "interaction_favorite_stats", stats.TableName())
}
