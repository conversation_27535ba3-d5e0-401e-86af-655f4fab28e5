package handler

import (
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"net/http"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/internal/content-cluster/interaction-service/external/service"
	"pxpat-backend/pkg/errors"
	"pxpat-backend/pkg/ksuid"
	globalTypes "pxpat-backend/pkg/types"
)

// FavoriteItemHandler 收藏项处理器
type FavoriteItemHandler struct {
	favoriteItemService *service.FavoriteItemService
}

// NewFavoriteItemHandler 创建收藏项处理器
func NewFavoriteItemHandler(favoriteItemService *service.FavoriteItemService) *FavoriteItemHandler {
	return &FavoriteItemHandler{
		favoriteItemService: favoriteItemService,
	}
}

// AddToFavorite 添加到收藏夹
func (h *FavoriteItemHandler) AddToFavorite(c *gin.Context) {
	userKSUID := ksuid.GetKSUID(c)

	var req dto.AddToFavoriteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Msg("添加收藏请求参数无效")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 参数验证
	if req.ContentKSUID == "" {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	gErr := h.favoriteItemService.AddToFavorite(c.Request.Context(), userKSUID, &req)
	if gErr != nil {
		log.Error().Err(gErr).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("添加收藏失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Msg("添加收藏成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
	})
}

// RemoveFromFavorite 从收藏夹移除
func (h *FavoriteItemHandler) RemoveFromFavorite(c *gin.Context) {
	userKSUID := ksuid.GetKSUID(c)

	var req dto.RemoveFromFavoriteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Msg("移除收藏请求参数无效")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	gErr := h.favoriteItemService.RemoveFromFavorite(c.Request.Context(), userKSUID, &req)
	if gErr != nil {
		log.Error().Err(gErr).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("移除收藏失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Msg("移除收藏成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
	})
}

// MoveFavoriteItem 移动收藏项
func (h *FavoriteItemHandler) MoveFavoriteItem(c *gin.Context) {
	userKSUID := ksuid.GetKSUID(c)

	var req dto.MoveFavoriteItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Msg("移动收藏项请求参数无效")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 参数验证
	if req.ContentKSUID == "" || req.TargetFolderID == "" {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	result, gErr := h.favoriteItemService.MoveFavoriteItem(c.Request.Context(), userKSUID, &req)
	if gErr != nil {
		log.Error().Err(gErr).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Str("target_folder_id", req.TargetFolderID).
			Msg("移动收藏项失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Str("target_folder_id", req.TargetFolderID).
		Msg("移动收藏项成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: result,
	})
}

// GetFavoriteItems 获取收藏项列表
func (h *FavoriteItemHandler) GetFavoriteItems(c *gin.Context) {
	userKSUID := ksuid.GetKSUID(c)

	// 解析查询参数
	var req dto.GetFavoriteItemsRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Msg("获取收藏项列表请求参数无效")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	if req.SortBy == "" {
		req.SortBy = "created_at"
	}
	if req.SortOrder == "" {
		req.SortOrder = "desc"
	}

	response, gErr := h.favoriteItemService.GetFavoriteItems(c.Request.Context(), userKSUID, &req)
	if gErr != nil {
		log.Error().Err(gErr).
			Str("user_ksuid", userKSUID).
			Str("folder_id", req.FavoriteFolderID).
			Msg("获取收藏项列表失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: response,
	})
}

// CheckFavoriteStatus 检查收藏状态
func (h *FavoriteItemHandler) CheckFavoriteStatus(c *gin.Context) {
	userKSUID := ksuid.GetKSUID(c)

	// 解析查询参数
	var req dto.CheckFavoriteStatusRequest
	req.ContentKSUID = c.Query("content_ksuid")

	// 参数验证
	if req.ContentKSUID == "" {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	response, gErr := h.favoriteItemService.CheckFavoriteStatus(c.Request.Context(), userKSUID, &req)
	if gErr != nil {
		log.Error().Err(gErr).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("检查收藏状态失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: response,
	})
}

// GetContentFavoriteStats 获取内容收藏统计
func (h *FavoriteItemHandler) GetContentFavoriteStats(c *gin.Context) {
	// 解析查询参数
	var req dto.GetContentFavoriteStatsRequest
	req.ContentKSUID = c.Query("content_ksuid")

	// 参数验证
	if req.ContentKSUID == "" {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	response, gErr := h.favoriteItemService.GetContentFavoriteStats(c.Request.Context(), req.ContentKSUID)
	if gErr != nil {
		log.Error().Err(gErr).
			Str("content_ksuid", req.ContentKSUID).
			Msg("获取内容收藏统计失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: response,
	})
}

// BatchGetContentFavoriteStats 批量获取内容收藏统计
func (h *FavoriteItemHandler) BatchGetContentFavoriteStats(c *gin.Context) {
	var req dto.BatchGetContentFavoriteStatsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).
			Msg("批量获取收藏统计请求参数无效")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 参数验证
	if len(req.ContentKSUIDs) == 0 {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	response, gErr := h.favoriteItemService.BatchGetContentFavoriteStats(c.Request.Context(), req.ContentKSUIDs)
	if gErr != nil {
		log.Error().Err(gErr).
			Int("count", len(req.ContentKSUIDs)).
			Msg("批量获取内容收藏统计失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: response,
	})
}

// GetTopFavoritedContent 获取最受欢迎的内容
func (h *FavoriteItemHandler) GetTopFavoritedContent(c *gin.Context) {
	// 解析查询参数
	var req dto.GetTopFavoritedContentRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error().Err(err).
			Msg("获取热门收藏内容请求参数无效")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 设置默认值
	if req.Limit <= 0 || req.Limit > 100 {
		req.Limit = 10
	}

	response, gErr := h.favoriteItemService.GetTopFavoritedContent(c.Request.Context(), req.ContentType, req.Limit)
	if gErr != nil {
		log.Error().Err(gErr).
			Str("content_type", req.ContentType).
			Int("limit", req.Limit).
			Msg("获取最受欢迎的内容失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: response,
	})
}
