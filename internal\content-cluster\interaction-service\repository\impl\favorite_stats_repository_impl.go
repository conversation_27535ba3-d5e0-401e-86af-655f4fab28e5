package impl

import (
	"context"
	"errors"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
	"pxpat-backend/internal/content-cluster/interaction-service/model"
	"pxpat-backend/internal/content-cluster/interaction-service/repository"
)

// favoriteStatsRepositoryImpl 收藏统计仓储实现
type favoriteStatsRepositoryImpl struct {
	db *gorm.DB
}

// NewFavoriteStatsRepository 创建收藏统计仓储实例
func NewFavoriteStatsRepository(db *gorm.DB) repository.FavoriteStatsRepository {
	return &favoriteStatsRepositoryImpl{db: db}
}

// CreateOrUpdateStats 创建或更新内容收藏统计
func (r *favoriteStatsRepositoryImpl) CreateOrUpdateStats(ctx context.Context, contentKSUID string, contentType model.ContentType, favoriteCount int64) error {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Str("content_type", string(contentType)).
		Int64("favorite_count", favoriteCount).
		Msg("开始创建或更新内容收藏统计")

	stats := &model.FavoriteStats{
		ContentKSUID:  contentKSUID,
		ContentType:   contentType,
		FavoriteCount: favoriteCount,
	}

	err := r.db.WithContext(ctx).
		Where("content_ksuid = ?", contentKSUID).
		Assign(map[string]interface{}{
			"favorite_count": favoriteCount,
		}).
		FirstOrCreate(stats).Error

	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Str("content_type", string(contentType)).
			Int64("favorite_count", favoriteCount).
			Msg("创建或更新内容收藏统计失败")
		return err
	}

	log.Debug().
		Str("content_ksuid", contentKSUID).
		Str("content_type", string(contentType)).
		Int64("favorite_count", favoriteCount).
		Msg("创建或更新内容收藏统计成功")

	return nil
}

// GetStatsByContentKSUID 根据内容ID获取统计信息
func (r *favoriteStatsRepositoryImpl) GetStatsByContentKSUID(ctx context.Context, contentKSUID string) (*model.FavoriteStats, error) {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Msg("开始获取内容收藏统计")

	var stats model.FavoriteStats
	err := r.db.WithContext(ctx).
		Where("content_ksuid = ?", contentKSUID).
		First(&stats).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Debug().
				Str("content_ksuid", contentKSUID).
				Msg("内容收藏统计不存在")
			return nil, nil
		}
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Msg("获取内容收藏统计失败")
		return nil, err
	}

	log.Debug().
		Str("content_ksuid", contentKSUID).
		Int64("favorite_count", stats.FavoriteCount).
		Msg("获取内容收藏统计成功")

	return &stats, nil
}

// BatchGetStatsByContentKSUIDs 批量获取内容统计信息
func (r *favoriteStatsRepositoryImpl) BatchGetStatsByContentKSUIDs(ctx context.Context, contentKSUIDs []string) (map[string]*model.FavoriteStats, error) {
	if len(contentKSUIDs) == 0 {
		return make(map[string]*model.FavoriteStats), nil
	}

	log.Debug().
		Int("count", len(contentKSUIDs)).
		Msg("开始批量获取内容收藏统计")

	var statsList []model.FavoriteStats
	err := r.db.WithContext(ctx).
		Where("content_ksuid IN ?", contentKSUIDs).
		Find(&statsList).Error

	if err != nil {
		log.Error().
			Err(err).
			Int("count", len(contentKSUIDs)).
			Msg("批量获取内容收藏统计失败")
		return nil, err
	}

	// 转换为map
	result := make(map[string]*model.FavoriteStats)
	for i := range statsList {
		result[statsList[i].ContentKSUID] = &statsList[i]
	}

	log.Debug().
		Int("request_count", len(contentKSUIDs)).
		Int("result_count", len(result)).
		Msg("批量获取内容收藏统计成功")

	return result, nil
}

// IncrementFavoriteCount 增加收藏数量
func (r *favoriteStatsRepositoryImpl) IncrementFavoriteCount(ctx context.Context, contentKSUID string, contentType model.ContentType) error {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Str("content_type", string(contentType)).
		Msg("开始增加内容收藏数量")

	// 先尝试更新现有记录
	result := r.db.WithContext(ctx).
		Model(&model.FavoriteStats{}).
		Where("content_ksuid = ?", contentKSUID).
		Update("favorite_count", gorm.Expr("favorite_count + 1"))

	if result.Error != nil {
		log.Error().
			Err(result.Error).
			Str("content_ksuid", contentKSUID).
			Msg("增加内容收藏数量失败")
		return result.Error
	}

	// 如果没有找到记录，创建新记录
	if result.RowsAffected == 0 {
		stats := model.NewFavoriteStats(contentKSUID, contentType)
		stats.FavoriteCount = 1

		err := r.db.WithContext(ctx).Create(stats).Error
		if err != nil {
			log.Error().
				Err(err).
				Str("content_ksuid", contentKSUID).
				Msg("创建内容收藏统计记录失败")
			return err
		}
	}

	log.Debug().
		Str("content_ksuid", contentKSUID).
		Msg("增加内容收藏数量成功")

	return nil
}

// DecrementFavoriteCount 减少收藏数量
func (r *favoriteStatsRepositoryImpl) DecrementFavoriteCount(ctx context.Context, contentKSUID string) error {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Msg("开始减少内容收藏数量")

	err := r.db.WithContext(ctx).
		Model(&model.FavoriteStats{}).
		Where("content_ksuid = ? AND favorite_count > 0", contentKSUID).
		Update("favorite_count", gorm.Expr("favorite_count - 1")).Error

	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Msg("减少内容收藏数量失败")
		return err
	}

	log.Debug().
		Str("content_ksuid", contentKSUID).
		Msg("减少内容收藏数量成功")

	return nil
}

// GetTopFavoritedContent 获取最受欢迎的内容（按收藏数排序）
func (r *favoriteStatsRepositoryImpl) GetTopFavoritedContent(ctx context.Context, contentType model.ContentType, limit int) ([]*model.FavoriteStats, error) {
	log.Debug().
		Str("content_type", string(contentType)).
		Int("limit", limit).
		Msg("开始获取最受欢迎的内容")

	var statsList []*model.FavoriteStats
	query := r.db.WithContext(ctx).
		Where("favorite_count > 0").
		Order("favorite_count DESC").
		Limit(limit)

	if contentType != "" {
		query = query.Where("content_type = ?", contentType)
	}

	err := query.Find(&statsList).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("content_type", string(contentType)).
			Int("limit", limit).
			Msg("获取最受欢迎的内容失败")
		return nil, err
	}

	log.Debug().
		Str("content_type", string(contentType)).
		Int("limit", limit).
		Int("result_count", len(statsList)).
		Msg("获取最受欢迎的内容成功")

	return statsList, nil
}

// GetStatsByContentType 按内容类型获取统计信息
func (r *favoriteStatsRepositoryImpl) GetStatsByContentType(ctx context.Context, contentType model.ContentType, page, pageSize int) ([]*model.FavoriteStats, int64, error) {
	log.Debug().
		Str("content_type", string(contentType)).
		Int("page", page).
		Int("page_size", pageSize).
		Msg("开始按内容类型获取收藏统计")

	var statsList []*model.FavoriteStats
	var total int64

	query := r.db.WithContext(ctx).Model(&model.FavoriteStats{})
	if contentType != "" {
		query = query.Where("content_type = ?", contentType)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("content_type", string(contentType)).
			Msg("获取收藏统计总数失败")
		return nil, 0, err
	}

	// 获取分页数据
	offset := (page - 1) * pageSize
	err = query.
		Order("favorite_count DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&statsList).Error

	if err != nil {
		log.Error().
			Err(err).
			Str("content_type", string(contentType)).
			Int("page", page).
			Int("page_size", pageSize).
			Msg("按内容类型获取收藏统计失败")
		return nil, 0, err
	}

	log.Debug().
		Str("content_type", string(contentType)).
		Int("page", page).
		Int("page_size", pageSize).
		Int64("total", total).
		Int("result_count", len(statsList)).
		Msg("按内容类型获取收藏统计成功")

	return statsList, total, nil
}

// RefreshStats 刷新指定内容的统计数据（从分表重新计算）
func (r *favoriteStatsRepositoryImpl) RefreshStats(ctx context.Context, contentKSUID string) error {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Msg("开始刷新内容收藏统计")

	// 获取唯一用户收藏数量
	count, err := r.GetUniqueUserCount(ctx, contentKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Msg("获取唯一用户收藏数量失败")
		return err
	}

	// 更新统计表
	if count > 0 {
		// 如果有收藏，更新或创建统计记录
		err = r.db.WithContext(ctx).
			Model(&model.FavoriteStats{}).
			Where("content_ksuid = ?", contentKSUID).
			Update("favorite_count", count).Error

		if err != nil {
			log.Error().
				Err(err).
				Str("content_ksuid", contentKSUID).
				Int64("count", count).
				Msg("更新内容收藏统计失败")
			return err
		}
	} else {
		// 如果没有收藏，删除统计记录
		err = r.DeleteStats(ctx, contentKSUID)
		if err != nil {
			log.Error().
				Err(err).
				Str("content_ksuid", contentKSUID).
				Msg("删除内容收藏统计失败")
			return err
		}
	}

	log.Debug().
		Str("content_ksuid", contentKSUID).
		Int64("count", count).
		Msg("刷新内容收藏统计成功")

	return nil
}

// BatchRefreshStats 批量刷新统计数据
func (r *favoriteStatsRepositoryImpl) BatchRefreshStats(ctx context.Context, contentKSUIDs []string) error {
	if len(contentKSUIDs) == 0 {
		return nil
	}

	log.Debug().
		Int("count", len(contentKSUIDs)).
		Msg("开始批量刷新内容收藏统计")

	for _, contentKSUID := range contentKSUIDs {
		err := r.RefreshStats(ctx, contentKSUID)
		if err != nil {
			log.Error().
				Err(err).
				Str("content_ksuid", contentKSUID).
				Msg("批量刷新内容收藏统计失败")
			return err
		}
	}

	log.Debug().
		Int("count", len(contentKSUIDs)).
		Msg("批量刷新内容收藏统计成功")

	return nil
}

// DeleteStats 删除统计记录
func (r *favoriteStatsRepositoryImpl) DeleteStats(ctx context.Context, contentKSUID string) error {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Msg("开始删除内容收藏统计")

	err := r.db.WithContext(ctx).
		Where("content_ksuid = ?", contentKSUID).
		Delete(&model.FavoriteStats{}).Error

	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Msg("删除内容收藏统计失败")
		return err
	}

	log.Debug().
		Str("content_ksuid", contentKSUID).
		Msg("删除内容收藏统计成功")

	return nil
}

// GetUniqueUserCount 获取内容的唯一收藏用户数量
func (r *favoriteStatsRepositoryImpl) GetUniqueUserCount(ctx context.Context, contentKSUID string) (int64, error) {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Msg("开始获取内容的唯一收藏用户数量")

	// 需要查询所有分表来统计唯一用户数量
	tableNames := model.GetAllFavoriteItemTableNames()
	var totalCount int64

	for _, tableName := range tableNames {
		var count int64
		err := r.db.WithContext(ctx).
			Table(tableName).
			Where("content_ksuid = ?", contentKSUID).
			Select("COUNT(DISTINCT user_ksuid)").
			Scan(&count).Error

		if err != nil {
			log.Error().
				Err(err).
				Str("content_ksuid", contentKSUID).
				Str("table_name", tableName).
				Msg("查询分表唯一用户数量失败")
			return 0, err
		}

		totalCount += count
	}

	log.Debug().
		Str("content_ksuid", contentKSUID).
		Int64("unique_user_count", totalCount).
		Msg("获取内容的唯一收藏用户数量成功")

	return totalCount, nil
}
