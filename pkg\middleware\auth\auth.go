package auth

import (
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v4"
	"net/http"
	"pxpat-backend/pkg/auth"
	globalTypes "pxpat-backend/pkg/types"
	"regexp"
	"strconv"
	"strings"
)

// UserAuthMiddleware 普通用户认证中间件
func UserAuthMiddleware(jwtManager auth.Manager) gin.HandlerFunc {
	return func(c *gin.Context) {
		allowNoLogin := []string{
			`^/api/v1/comment$`,
			`^/api/v1/comment/replies$`,
			`^/api/v1/content/.*`,
			`^/api/v1/like/user`,
			`^/api/v1/favorite/user`,
			`^/api/v1/favorites/folders$`,
			`^/api/v1/favorites/folders/.*`,
			`^/api/v1/favorites/items$`,
			`^/api/v1/favorites/folders&`,
		}

		claims, err := parseToken(c, jwtManager)
		if err != nil {
			// 用正则表达式匹配path
			for _, v := range allowNoLogin {
				mattched, err := regexp.MatchString(v, c.Request.URL.Path)
				if err == nil && mattched {
					c.Set("user_ksuid", "")
					c.Set("id", 0)
					c.Next()
					return
				}
			}

			c.JSON(http.StatusUnauthorized, globalTypes.GlobalResponse{
				Code:    401,
				Message: err.Error(),
				Data:    nil,
			})
			c.Abort()
			return
		}

		// 处理ks_uid，可能是字符串或浮点数
		var KsUID string
		KsUID, ok := claims["user_ksuid"].(string)
		if !ok {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "认证失败", "message": "无效的用户KSUID类型", "code": 401})
			c.Abort()
			return
		}

		// 处理id，可能是字符串或浮点数

		var ID float64
		ID, ok = claims["id"].(float64)
		if !ok {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "认证失败", "message": "无效的用户ID类型", "code": 401})
			c.Abort()
			return
		}

		// 将用户ID存入上下文
		c.Set("user_ksuid", KsUID)
		c.Set("id", ID)
		c.Next()
	}
}

// AdminAuthMiddleware 管理员认证中间件
func AdminAuthMiddleware(jwtManager auth.Manager) gin.HandlerFunc {
	return func(c *gin.Context) {
		claims, err := parseToken(c, jwtManager)
		if err != nil {
			c.JSON(http.StatusUnauthorized, globalTypes.GlobalResponse{
				Code:    401,
				Message: err.Error(),
				Data:    nil,
			})
			c.Abort()
			return
		}

		// 检查是否是管理员令牌
		isAdmin, exists := claims["is_admin"]
		if !exists || !isAdmin.(bool) {
			c.JSON(http.StatusForbidden, gin.H{"error": "权限不足", "message": "需要管理员权限", "code": 403})
			c.Abort()
			return
		}

		// 将管理员ID和角色存储在上下文中
		// 根据user_id的类型进行处理
		var adminID uint
		switch userID := claims["user_id"].(type) {
		case float64:
			// 如果是浮点数，转换为uint
			adminID = uint(userID)
		case string:
			// 如果是字符串，先解析为uint64再转换为uint
			id, err := strconv.ParseUint(userID, 10, 64)
			if err != nil {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "认证失败", "message": "无效的用户ID格式", "code": 401})
				c.Abort()
				return
			}
			adminID = uint(id)
		default:
			// 其他类型，返回错误
			c.JSON(http.StatusUnauthorized, gin.H{"error": "认证失败", "message": "无效的用户ID类型", "code": 401})
			c.Abort()
			return
		}

		c.Set("adminID", adminID)
		c.Set("user_id", fmt.Sprintf("%d", adminID)) // 兼容用户ID格式

		if role, exists := claims["role"]; exists {
			c.Set("adminRole", role.(string))
		} else {
			c.Set("adminRole", "admin") // 默认角色
		}

		c.Set("isAdmin", true)
		c.Next()
	}
}

// SuperAdminMiddleware 超级管理员权限中间件
func SuperAdminMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		role, exists := c.Get("adminRole")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "认证失败", "message": "请先登录", "code": 401})
			c.Abort()
			return
		}

		if role != "superAdmin" {
			c.JSON(http.StatusForbidden, gin.H{"error": "权限不足", "message": "需要超级管理员权限", "code": 403})
			c.Abort()
			return
		}

		c.Next()
	}
}

// ServiceAuthMiddleware 服务间认证中间件
func ServiceAuthMiddleware(serviceToken string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取Authorization头
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, globalTypes.GlobalResponse{
				Code:    401,
				Message: "Missing authorization header",
				Data:    nil,
			})
			c.Abort()
			return
		}

		// 验证Bearer token格式
		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, globalTypes.GlobalResponse{
				Code:    401,
				Message: "Invalid authorization format",
				Data:    nil,
			})
			c.Abort()
			return
		}

		// 验证token
		token := parts[1]
		if token != serviceToken {
			c.JSON(http.StatusUnauthorized, globalTypes.GlobalResponse{
				Code:    401,
				Message: "Invalid service token",
				Data:    nil,
			})
			c.Abort()
			return
		}

		// 验证通过，继续处理请求
		c.Next()
	}
}

func parseToken(c *gin.Context, jwtManager auth.Manager) (jwt.MapClaims, error) {
	head := c.GetHeader("Authorization")
	// 获取
	if head == "" {
		return nil, errors.New("未提供认证信息")
	}
	token := strings.TrimPrefix(head, "Bearer ")
	if token == head {
		return nil, errors.New("认证格式无效")
	}
	// 校验
	claims, err := jwtManager.VerifyToken(token)
	if err != nil {
		return nil, errors.New("无效或已过期的令牌")
	}
	return claims, nil
}
