package service

import (
	externalService "pxpat-backend/internal/content-cluster/interaction-service/external/service"
	intraService "pxpat-backend/internal/content-cluster/interaction-service/intra/service"
)

// Services 服务层集合
type Services struct {
	// Album相关服务
	AlbumService        *externalService.AlbumService
	AlbumContentService *externalService.AlbumContentService

	// Favorite相关服务
	FavoriteFolderService   *externalService.FavoriteFolderService
	FavoriteItemService     *externalService.FavoriteItemService
	InternalFavoriteService *intraService.InternalFavoriteService

	// Like相关服务
	LikeService         *externalService.LikeService
	InternalLikeService *intraService.InternalLikeService

	// PlayHistory相关服务
	PlayHistoryService         *externalService.PlayHistoryService
	InternalPlayHistoryService *intraService.InternalPlayHistoryService

	// 其他交互相关服务（预留）
	// CommentService  CommentService
	// ShareService    ShareService
	// RewardService   RewardService
}

// NewServices 创建服务层集合实例
func NewServices(
	// Album相关
	albumService *externalService.AlbumService,
	albumContentService *externalService.AlbumContentService,
	// Favorite相关
	favoriteFolderService *externalService.FavoriteFolderService,
	favoriteItemService *externalService.FavoriteItemService,
	internalFavoriteService *intraService.InternalFavoriteService,
	// Like相关
	likeService *externalService.LikeService,
	internalLikeService *intraService.InternalLikeService,
	// PlayHistory相关
	playHistoryService *externalService.PlayHistoryService,
	internalPlayHistoryService *intraService.InternalPlayHistoryService,
) *Services {
	return &Services{
		// Album相关
		AlbumService:        albumService,
		AlbumContentService: albumContentService,

		// Favorite相关
		FavoriteFolderService:   favoriteFolderService,
		FavoriteItemService:     favoriteItemService,
		InternalFavoriteService: internalFavoriteService,

		// Like相关
		LikeService:         likeService,
		InternalLikeService: internalLikeService,

		// PlayHistory相关
		PlayHistoryService:         playHistoryService,
		InternalPlayHistoryService: internalPlayHistoryService,

		// 其他交互相关（暂时为nil，后续实现）
		// CommentService: nil,
		// ShareService:   nil,
		// RewardService:  nil,
	}
}

// GetFavoriteFolderService 获取收藏夹服务
func (s *Services) GetFavoriteFolderService() *externalService.FavoriteFolderService {
	return s.FavoriteFolderService
}

// GetFavoriteItemService 获取收藏项服务
func (s *Services) GetFavoriteItemService() *externalService.FavoriteItemService {
	return s.FavoriteItemService
}

// GetInternalFavoriteService 获取内部收藏服务
func (s *Services) GetInternalFavoriteService() *intraService.InternalFavoriteService {
	return s.InternalFavoriteService
}

// GetAlbumService 获取合集服务
func (s *Services) GetAlbumService() *externalService.AlbumService {
	return s.AlbumService
}

// GetAlbumContentService 获取合集内容服务
func (s *Services) GetAlbumContentService() *externalService.AlbumContentService {
	return s.AlbumContentService
}

// GetLikeService 获取点赞服务
func (s *Services) GetLikeService() *externalService.LikeService {
	return s.LikeService
}

// GetInternalLikeService 获取内部点赞服务
func (s *Services) GetInternalLikeService() *intraService.InternalLikeService {
	return s.InternalLikeService
}

// GetPlayHistoryService 获取播放历史服务
func (s *Services) GetPlayHistoryService() *externalService.PlayHistoryService {
	return s.PlayHistoryService
}

// GetInternalPlayHistoryService 获取内部播放历史服务
func (s *Services) GetInternalPlayHistoryService() *intraService.InternalPlayHistoryService {
	return s.InternalPlayHistoryService
}
