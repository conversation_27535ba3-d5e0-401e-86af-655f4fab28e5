package migrations

import (
	"gorm.io/gorm"
	"pxpat-backend/internal/user-cluster/user-service/model"
)

// Migrates 进行数据库迁移
// 控制整个服务的数据库迁移
func Migrates(db *gorm.DB) error {
	err := db.AutoMigrate(
		// 用户相关模型
		&model.User{},
		&model.UserAlias{},  // 添加用户别名模型
		&model.UserFollow{}, // 添加用户关注模型
		&model.Verification{},
		&model.Role{},
		&model.RolePermission{},
		&model.ExperienceRecord{}, // 添加经验值记录模型
		&model.ReputationRecord{}, // 添加声望记录模型

	)
	if err != nil {
		return err
	}

	return nil
}
