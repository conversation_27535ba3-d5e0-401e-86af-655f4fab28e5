package impl

import (
	"context"
	"errors"
	"fmt"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
	"pxpat-backend/internal/content-cluster/interaction-service/model"
	"pxpat-backend/internal/content-cluster/interaction-service/repository"
	repositoryErrors "pxpat-backend/pkg/errors/repository"
)

// favoriteItemRepositoryImpl 收藏项仓储实现
type favoriteItemRepositoryImpl struct {
	db *gorm.DB
}

// NewFavoriteItemRepository 创建收藏项仓储实例
func NewFavoriteItemRepository(db *gorm.DB) repository.FavoriteItemRepository {
	return &favoriteItemRepositoryImpl{db: db}
}

// getTableName 根据用户KSUID获取对应的分表名
func (r *favoriteItemRepositoryImpl) getTableName(userKSUID string) string {
	return model.GetFavoriteItemTableName(userKSUID)
}

// BatchCreate 批量创建收藏项
func (r *favoriteItemRepositoryImpl) BatchCreate(ctx context.Context, items []*model.FavoriteItem) error {
	if len(items) == 0 {
		return nil
	}

	// 按表名分组
	tableGroups := make(map[string][]*model.FavoriteItem)
	for _, item := range items {
		tableName := r.getTableName(item.UserKSUID)
		tableGroups[tableName] = append(tableGroups[tableName], item)
	}

	// 分表批量插入
	for tableName, groupItems := range tableGroups {
		result := r.db.WithContext(ctx).Table(tableName).CreateInBatches(groupItems, 100)
		if result.Error != nil {
			log.Error().Err(result.Error).
				Str("table_name", tableName).
				Int("items_count", len(groupItems)).
				Msg("批量创建收藏项失败")
			return result.Error
		}

		log.Debug().
			Str("table_name", tableName).
			Int("items_count", len(groupItems)).
			Msg("收藏项批量创建成功")
	}

	return nil
}

// Delete 删除收藏项
func (r *favoriteItemRepositoryImpl) Delete(ctx context.Context, itemID string, userKSUID string) error {
	tableName := r.getTableName(userKSUID)

	result := r.db.WithContext(ctx).
		Table(tableName).
		Where("favorite_item_id = ? AND user_ksuid = ?", itemID, userKSUID).
		Delete(&model.FavoriteItem{})

	if result.Error != nil {
		log.Error().Err(result.Error).
			Str("item_id", itemID).
			Str("user_ksuid", userKSUID).
			Str("table_name", tableName).
			Msg("删除收藏项失败")
		return result.Error
	}

	if result.RowsAffected == 0 {
		return repositoryErrors.ErrFavoriteItemNotFound
	}

	log.Info().
		Str("item_id", itemID).
		Str("user_ksuid", userKSUID).
		Msg("收藏项删除成功")

	return nil
}

// DeleteByContentKSUID 根据内容ID删除收藏项
func (r *favoriteItemRepositoryImpl) DeleteByContentKSUID(ctx context.Context, userKSUID, contentKSUID string) error {
	tableName := r.getTableName(userKSUID)

	result := r.db.WithContext(ctx).
		Table(tableName).
		Where("user_ksuid = ? AND content_ksuid = ?", userKSUID, contentKSUID).
		Delete(&model.FavoriteItem{})

	if result.Error != nil {
		log.Error().Err(result.Error).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", contentKSUID).
			Str("table_name", tableName).
			Msg("根据内容KSUID删除收藏项失败")
		return result.Error
	}

	if result.RowsAffected == 0 {
		return repositoryErrors.ErrFavoriteItemNotFound
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", contentKSUID).
		Int64("deleted_count", result.RowsAffected).
		Msg("根据内容KSUID删除收藏项成功")

	return nil
}

// DeleteByFolderKSUID 根据收藏夹ID删除所有收藏项
func (r *favoriteItemRepositoryImpl) DeleteByFolderKSUID(ctx context.Context, userKSUID, folderKSUID string) error {
	tableName := r.getTableName(userKSUID)

	result := r.db.WithContext(ctx).
		Table(tableName).
		Where("user_ksuid = ? AND favorite_folder_id = ?", userKSUID, folderKSUID).
		Delete(&model.FavoriteItem{})

	if result.Error != nil {
		log.Error().Err(result.Error).
			Str("user_ksuid", userKSUID).
			Str("folder_id", folderKSUID).
			Str("table_name", tableName).
			Msg("根据收藏夹ID删除收藏项失败")
		return result.Error
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("folder_id", folderKSUID).
		Int64("affected_rows", result.RowsAffected).
		Msg("根据收藏夹ID删除收藏项成功")

	return nil
}

// GetByID 根据ID获取收藏项
func (r *favoriteItemRepositoryImpl) GetByID(ctx context.Context, itemID string, userKSUID string) (*model.FavoriteItem, error) {
	var item model.FavoriteItem
	tableName := r.getTableName(userKSUID)

	result := r.db.WithContext(ctx).
		Table(tableName).
		Where("favorite_item_id = ? AND user_ksuid = ?", itemID, userKSUID).
		First(&item)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, repositoryErrors.ErrFavoriteItemNotFound
		}
		log.Error().Err(result.Error).
			Str("item_id", itemID).
			Str("user_ksuid", userKSUID).
			Str("table_name", tableName).
			Msg("根据ID获取收藏项失败")
		return nil, result.Error
	}

	return &item, nil
}

// GetByContentKSUID 根据内容ID获取用户的收藏项
func (r *favoriteItemRepositoryImpl) GetByContentKSUID(ctx context.Context, userKSUID, contentKSUID string) (*model.FavoriteItem, error) {
	var item model.FavoriteItem
	tableName := r.getTableName(userKSUID)

	result := r.db.WithContext(ctx).
		Table(tableName).
		Where("user_ksuid = ? AND content_ksuid = ?", userKSUID, contentKSUID).
		First(&item)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, repositoryErrors.ErrFavoriteItemNotFound
		}
		log.Error().Err(result.Error).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", contentKSUID).
			Str("table_name", tableName).
			Msg("根据内容KSUID获取收藏项失败")
		return nil, result.Error
	}

	return &item, nil
}

// GetByFolderKSUID 获取收藏夹中的收藏项
func (r *favoriteItemRepositoryImpl) GetByFolderKSUID(ctx context.Context, userKSUID, folderID string, page, pageSize int) ([]*model.FavoriteItem, int64, error) {
	tableName := r.getTableName(userKSUID)
	var items []*model.FavoriteItem
	var total int64

	// 计算总数
	countResult := r.db.WithContext(ctx).
		Table(tableName).
		Where("user_ksuid = ? AND favorite_folder_id = ?", userKSUID, folderID).
		Count(&total)

	if countResult.Error != nil {
		log.Error().Err(countResult.Error).
			Str("user_ksuid", userKSUID).
			Str("folder_id", folderID).
			Str("table_name", tableName).
			Msg("统计收藏夹收藏项数量失败")
		return nil, 0, countResult.Error
	}

	// 分页查询
	offset := (page - 1) * pageSize
	result := r.db.WithContext(ctx).
		Table(tableName).
		Where("user_ksuid = ? AND favorite_folder_id = ?", userKSUID, folderID).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&items)

	if result.Error != nil {
		log.Error().Err(result.Error).
			Str("user_ksuid", userKSUID).
			Str("folder_id", folderID).
			Int("page", page).
			Int("page_size", pageSize).
			Str("table_name", tableName).
			Msg("根据收藏夹获取收藏项失败")
		return nil, 0, result.Error
	}

	return items, total, nil
}

// GetByFolderAndContentType 按收藏夹和内容类型获取收藏项
func (r *favoriteItemRepositoryImpl) GetByFolderAndContentType(ctx context.Context, userKSUID, folderID string, contentType model.ContentType, page, pageSize int) ([]*model.FavoriteItem, int64, error) {
	tableName := r.getTableName(userKSUID)
	var items []*model.FavoriteItem
	var total int64

	// 计算总数
	countResult := r.db.WithContext(ctx).
		Table(tableName).
		Where("user_ksuid = ? AND favorite_folder_id = ? AND content_type = ?", userKSUID, folderID, contentType).
		Count(&total)

	if countResult.Error != nil {
		log.Error().Err(countResult.Error).
			Str("user_ksuid", userKSUID).
			Str("folder_id", folderID).
			Str("content_type", string(contentType)).
			Str("table_name", tableName).
			Msg("统计收藏夹和内容类型的收藏项数量失败")
		return nil, 0, countResult.Error
	}

	// 分页查询
	offset := (page - 1) * pageSize
	result := r.db.WithContext(ctx).
		Table(tableName).
		Where("user_ksuid = ? AND favorite_folder_id = ? AND content_type = ?", userKSUID, folderID, contentType).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&items)

	if result.Error != nil {
		log.Error().Err(result.Error).
			Str("user_ksuid", userKSUID).
			Str("folder_id", folderID).
			Str("content_type", string(contentType)).
			Int("page", page).
			Int("page_size", pageSize).
			Str("table_name", tableName).
			Msg("根据收藏夹和内容类型获取收藏项失败")
		return nil, 0, result.Error
	}

	return items, total, nil
}

// GetByUserKSUID 获取用户的所有收藏项
func (r *favoriteItemRepositoryImpl) GetByUserKSUID(ctx context.Context, userKSUID string, page, pageSize int) ([]*model.FavoriteItem, int64, error) {
	tableName := r.getTableName(userKSUID)
	var items []*model.FavoriteItem
	var total int64

	// 计算总数
	countResult := r.db.WithContext(ctx).
		Table(tableName).
		Where("user_ksuid = ?", userKSUID).
		Count(&total)

	if countResult.Error != nil {
		log.Error().Err(countResult.Error).
			Str("user_ksuid", userKSUID).
			Str("table_name", tableName).
			Msg("统计用户收藏项数量失败")
		return nil, 0, countResult.Error
	}

	// 分页查询
	offset := (page - 1) * pageSize
	result := r.db.WithContext(ctx).
		Table(tableName).
		Where("user_ksuid = ?", userKSUID).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&items)

	if result.Error != nil {
		log.Error().Err(result.Error).
			Str("user_ksuid", userKSUID).
			Int("page", page).
			Int("page_size", pageSize).
			Str("table_name", tableName).
			Msg("获取用户收藏项失败")
		return nil, 0, result.Error
	}

	return items, total, nil
}

// GetByUserAndContentType 按用户和内容类型获取收藏项
func (r *favoriteItemRepositoryImpl) GetByUserAndContentType(ctx context.Context, userKSUID string, contentType model.ContentType, page, pageSize int) ([]*model.FavoriteItem, int64, error) {
	tableName := r.getTableName(userKSUID)
	var items []*model.FavoriteItem
	var total int64

	// 计算总数
	countResult := r.db.WithContext(ctx).
		Table(tableName).
		Where("user_ksuid = ? AND content_type = ?", userKSUID, contentType).
		Count(&total)

	if countResult.Error != nil {
		log.Error().Err(countResult.Error).
			Str("user_ksuid", userKSUID).
			Str("content_type", string(contentType)).
			Str("table_name", tableName).
			Msg("统计用户按内容类型的收藏项数量失败")
		return nil, 0, countResult.Error
	}

	// 分页查询
	offset := (page - 1) * pageSize
	result := r.db.WithContext(ctx).
		Table(tableName).
		Where("user_ksuid = ? AND content_type = ?", userKSUID, contentType).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&items)

	if result.Error != nil {
		log.Error().Err(result.Error).
			Str("user_ksuid", userKSUID).
			Str("content_type", string(contentType)).
			Int("page", page).
			Int("page_size", pageSize).
			Str("table_name", tableName).
			Msg("根据用户和内容类型获取收藏项失败")
		return nil, 0, result.Error
	}

	return items, total, nil
}

// MoveTo 移动收藏项到指定收藏夹
func (r *favoriteItemRepositoryImpl) MoveTo(ctx context.Context, itemID, userKSUID, targetFolderID string) error {
	tableName := r.getTableName(userKSUID)

	result := r.db.WithContext(ctx).
		Table(tableName).
		Where("favorite_item_id = ? AND user_ksuid = ?", itemID, userKSUID).
		Update("favorite_folder_id", targetFolderID)

	if result.Error != nil {
		log.Error().Err(result.Error).
			Str("item_id", itemID).
			Str("user_ksuid", userKSUID).
			Str("target_folder_id", targetFolderID).
			Str("table_name", tableName).
			Msg("移动收藏项失败")
		return result.Error
	}

	if result.RowsAffected == 0 {
		return repositoryErrors.ErrFavoriteItemNotFound
	}

	log.Info().
		Str("item_id", itemID).
		Str("user_ksuid", userKSUID).
		Str("target_folder_id", targetFolderID).
		Msg("收藏项移动成功")

	return nil
}

// BatchMoveTo 批量移动收藏项
func (r *favoriteItemRepositoryImpl) BatchMoveTo(ctx context.Context, itemIDs []string, userKSUID, targetFolderID string) error {
	if len(itemIDs) == 0 {
		return nil
	}

	tableName := r.getTableName(userKSUID)

	result := r.db.WithContext(ctx).
		Table(tableName).
		Where("favorite_item_id IN ? AND user_ksuid = ?", itemIDs, userKSUID).
		Update("favorite_folder_id", targetFolderID)

	if result.Error != nil {
		log.Error().Err(result.Error).
			Strs("item_ids", itemIDs).
			Str("user_ksuid", userKSUID).
			Str("target_folder_id", targetFolderID).
			Str("table_name", tableName).
			Msg("批量移动收藏项失败")
		return result.Error
	}

	log.Info().
		Strs("item_ids", itemIDs).
		Str("user_ksuid", userKSUID).
		Str("target_folder_id", targetFolderID).
		Int64("moved_count", result.RowsAffected).
		Msg("收藏项批量移动成功")

	return nil
}

// GetRecentFavorites 获取用户最近收藏的内容
func (r *favoriteItemRepositoryImpl) GetRecentFavorites(ctx context.Context, userKSUID string, limit int) ([]*model.FavoriteItem, error) {
	tableName := r.getTableName(userKSUID)
	var items []*model.FavoriteItem

	result := r.db.WithContext(ctx).
		Table(tableName).
		Where("user_ksuid = ?", userKSUID).
		Order("created_at DESC").
		Limit(limit).
		Find(&items)

	if result.Error != nil {
		log.Error().Err(result.Error).
			Str("user_ksuid", userKSUID).
			Int("limit", limit).
			Str("table_name", tableName).
			Msg("获取最近收藏失败")
		return nil, result.Error
	}

	return items, nil
}

// SearchFavorites 搜索用户的收藏项
func (r *favoriteItemRepositoryImpl) SearchFavorites(ctx context.Context, userKSUID, keyword string, page, pageSize int) ([]*model.FavoriteItem, int64, error) {
	// 注意：这个方法需要根据实际的内容表结构来实现搜索
	// 这里只是一个基础实现，实际使用时可能需要联表查询内容表
	tableName := r.getTableName(userKSUID)
	var items []*model.FavoriteItem
	var total int64

	// 这里简单按内容KSUID搜索，实际应该联表查询内容标题等
	searchPattern := "%" + keyword + "%"

	// 计算总数
	countResult := r.db.WithContext(ctx).
		Table(tableName).
		Where("user_ksuid = ? AND content_ksuid LIKE ?", userKSUID, searchPattern).
		Count(&total)

	if countResult.Error != nil {
		log.Error().Err(countResult.Error).
			Str("user_ksuid", userKSUID).
			Str("keyword", keyword).
			Str("table_name", tableName).
			Msg("统计搜索结果数量失败")
		return nil, 0, countResult.Error
	}

	// 分页查询
	offset := (page - 1) * pageSize
	result := r.db.WithContext(ctx).
		Table(tableName).
		Where("user_ksuid = ? AND content_ksuid LIKE ?", userKSUID, searchPattern).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&items)

	if result.Error != nil {
		log.Error().Err(result.Error).
			Str("user_ksuid", userKSUID).
			Str("keyword", keyword).
			Int("page", page).
			Int("page_size", pageSize).
			Str("table_name", tableName).
			Msg("搜索收藏项失败")
		return nil, 0, result.Error
	}

	return items, total, nil
}

// GetFavoritesByContentKSUIDs 批量获取内容的收藏状态
func (r *favoriteItemRepositoryImpl) GetFavoritesByContentKSUIDs(ctx context.Context, userKSUID string, contentKSUIDs []string) (map[string]*model.FavoriteItem, error) {
	if len(contentKSUIDs) == 0 {
		return make(map[string]*model.FavoriteItem), nil
	}

	tableName := r.getTableName(userKSUID)
	var items []*model.FavoriteItem

	result := r.db.WithContext(ctx).
		Table(tableName).
		Where("user_ksuid = ? AND content_ksuid IN ?", userKSUID, contentKSUIDs).
		Find(&items)

	if result.Error != nil {
		log.Error().Err(result.Error).
			Str("user_ksuid", userKSUID).
			Strs("content_ksuids", contentKSUIDs).
			Str("table_name", tableName).
			Msg("根据内容KSUID批量获取收藏项失败")
		return nil, result.Error
	}

	// 转换为map
	favoriteMap := make(map[string]*model.FavoriteItem)
	for _, item := range items {
		favoriteMap[item.ContentKSUID] = item
	}

	return favoriteMap, nil
}

// CountByFolder 统计收藏夹中的收藏项数量
func (r *favoriteItemRepositoryImpl) CountByFolder(ctx context.Context, folderID string) (int64, error) {
	// 需要遍历所有分表来统计
	var totalCount int64

	for i := 0; i < 16; i++ {
		tableName := fmt.Sprintf("interaction_favorite_items_%d", i)

		// 检查表是否存在
		if !r.db.Migrator().HasTable(tableName) {
			continue
		}

		var count int64
		result := r.db.WithContext(ctx).
			Table(tableName).
			Where("favorite_folder_id = ?", folderID).
			Count(&count)

		if result.Error != nil {
			log.Error().Err(result.Error).
				Str("folder_id", folderID).
				Str("table_name", tableName).
				Msg("统计表中收藏项数量失败")
			continue
		}

		totalCount += count
	}

	return totalCount, nil
}

// CountByUser 统计用户的收藏项总数
func (r *favoriteItemRepositoryImpl) CountByUser(ctx context.Context, userKSUID string) (int64, error) {
	tableName := r.getTableName(userKSUID)
	var count int64

	result := r.db.WithContext(ctx).
		Table(tableName).
		Where("user_ksuid = ?", userKSUID).
		Count(&count)

	if result.Error != nil {
		log.Error().Err(result.Error).
			Str("user_ksuid", userKSUID).
			Str("table_name", tableName).
			Msg("统计用户收藏项数量失败")
		return 0, result.Error
	}

	return count, nil
}

// CountByContentType 按内容类型统计收藏项数量
func (r *favoriteItemRepositoryImpl) CountByContentType(ctx context.Context, userKSUID string, contentType model.ContentType) (int64, error) {
	tableName := r.getTableName(userKSUID)
	var count int64

	result := r.db.WithContext(ctx).
		Table(tableName).
		Where("user_ksuid = ? AND content_type = ?", userKSUID, contentType).
		Count(&count)

	if result.Error != nil {
		log.Error().Err(result.Error).
			Str("user_ksuid", userKSUID).
			Str("content_type", string(contentType)).
			Str("table_name", tableName).
			Msg("按内容类型统计用户收藏项数量失败")
		return 0, result.Error
	}

	return count, nil
}

// GetByUserAndContentKSUID 根据用户和内容ID获取所有收藏项
func (r *favoriteItemRepositoryImpl) GetByUserAndContentKSUID(ctx context.Context, userKSUID, contentKSUID string) ([]*model.FavoriteItem, error) {
	tableName := r.getTableName(userKSUID)
	var items []*model.FavoriteItem

	result := r.db.WithContext(ctx).
		Table(tableName).
		Where("user_ksuid = ? AND content_ksuid = ?", userKSUID, contentKSUID).
		Find(&items)

	if result.Error != nil {
		log.Error().Err(result.Error).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", contentKSUID).
			Str("table_name", tableName).
			Msg("根据用户和内容KSUID获取收藏项失败")
		return nil, result.Error
	}

	if len(items) == 0 {
		return nil, repositoryErrors.ErrFavoriteItemNotFound
	}

	return items, nil
}

// ExistsByUserAndContent 检查用户是否收藏了指定内容
func (r *favoriteItemRepositoryImpl) ExistsByUserAndContent(ctx context.Context, userKSUID, contentKSUID string) (bool, error) {
	tableName := r.getTableName(userKSUID)
	var count int64

	result := r.db.WithContext(ctx).
		Table(tableName).
		Where("user_ksuid = ? AND content_ksuid = ?", userKSUID, contentKSUID).
		Count(&count)

	if result.Error != nil {
		log.Error().Err(result.Error).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", contentKSUID).
			Str("table_name", tableName).
			Msg("检查收藏是否存在失败")
		return false, result.Error
	}

	return count > 0, nil
}

// GetByUserAndContent 根据用户和内容获取收藏项
func (r *favoriteItemRepositoryImpl) GetByUserAndContent(ctx context.Context, userKSUID, contentKSUID string) ([]*model.FavoriteItem, error) {
	// 这个方法和GetByUserAndContentKSUID功能相同
	return r.GetByUserAndContentKSUID(ctx, userKSUID, contentKSUID)
}

// GetUserItemStats 获取用户收藏项统计
func (r *favoriteItemRepositoryImpl) GetUserItemStats(ctx context.Context, userKSUID string) (map[string]int, error) {
	tableName := r.getTableName(userKSUID)
	stats := make(map[string]int)

	// 按内容类型统计
	var results []struct {
		ContentType string `json:"content_type"`
		Count       int    `json:"count"`
	}

	result := r.db.WithContext(ctx).
		Table(tableName).
		Select("content_type, COUNT(*) as count").
		Where("user_ksuid = ?", userKSUID).
		Group("content_type").
		Find(&results)

	if result.Error != nil {
		log.Error().Err(result.Error).
			Str("user_ksuid", userKSUID).
			Str("table_name", tableName).
			Msg("获取用户收藏项统计失败")
		return nil, result.Error
	}

	for _, r := range results {
		stats[r.ContentType] = r.Count
	}

	return stats, nil
}

// BatchCheckFavoriteStatus 批量检查收藏状态
func (r *favoriteItemRepositoryImpl) BatchCheckFavoriteStatus(ctx context.Context, userKSUID string, contentKSUIDs []string) (map[string]bool, error) {
	if len(contentKSUIDs) == 0 {
		return make(map[string]bool), nil
	}

	tableName := r.getTableName(userKSUID)
	var items []*model.FavoriteItem

	result := r.db.WithContext(ctx).
		Table(tableName).
		Select("content_ksuid").
		Where("user_ksuid = ? AND content_ksuid IN ?", userKSUID, contentKSUIDs).
		Find(&items)

	if result.Error != nil {
		log.Error().Err(result.Error).
			Str("user_ksuid", userKSUID).
			Strs("content_ksuids", contentKSUIDs).
			Str("table_name", tableName).
			Msg("批量检查收藏状态失败")
		return nil, result.Error
	}

	// 构建结果映射
	statusMap := make(map[string]bool)

	// 初始化所有内容为未收藏
	for _, contentKSUID := range contentKSUIDs {
		statusMap[contentKSUID] = false
	}

	// 设置已收藏的内容
	for _, item := range items {
		statusMap[item.ContentKSUID] = true
	}

	return statusMap, nil
}
